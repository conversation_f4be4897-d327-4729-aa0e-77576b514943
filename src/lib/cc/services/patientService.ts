import { dbSchema, getDb } from "@database";
import type { GetCCPatientType } from "@libCC/CCTypes";
import { CCPatientRequest } from "@libCC/request/patientRequest";
import { eq } from "drizzle-orm";

export class PatientService {
  private request: CCPatientRequest;
  private query = getDb().query.patient;
  private db = getDb();

  constructor() {
    this.request = new CCPatientRequest();
    this.query = getDb().query.patient;
    this.db = getDb();
  }

  async sync(patient: GetCCPatientType) {
    const localPatient = await this.upsertLocal(patient);
    return localPatient;
  }

  async getPatientById(id: number): Promise<GetCCPatientType> {
    return this.request.get(id);
  }

  async searchPatient(
    searchTerm: string | string[]
  ): Promise<GetCCPatientType | null> {
    if (Array.isArray(searchTerm)) {
      // Search with multiple terms and return the first match found
      for (const term of searchTerm) {
        const result = await this.request.search(term);
        if (result !== null) {
          return result; // Return first match found
        }
      }
      return null; // No matches found
    }
    return this.request.search(searchTerm);
  }

  async upsertLocal(
    patient: GetCCPatientType
  ): Promise<typeof dbSchema.patient.$inferSelect> {
    const query = await this.query.findFirst({
      where: eq(dbSchema.patient.ccId, patient.id),
    });

    if (query) {
      return query;
    }

    const create = await this.db
      .insert(dbSchema.patient)
      .values({
        ccId: patient.id,
        ccData: patient,
      })
      .returning();

    return create[0];
  }

  async isInBu
}
