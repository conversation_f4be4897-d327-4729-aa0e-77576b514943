import type { GetCCPatientType } from "@libCC/CCTypes";
import { CCPatientRequest } from "@libCC/request/patientRequest";
export class PatientService {
  private request: CCPatientRequest;

  constructor() {
    this.request = new CCPatientRequest();
  }

  async getPatientById(id: number): Promise<GetCCPatientType> {
    return this.request.getPatient(id);
  }

  async searchPatient(searchTerm: string | string[]): Promise<GetCCPatientType | null> {
    if (Array.isArray(searchTerm)) {
      const searchResults = await Promise.all(
        searchTerm.map((term) => this.request.searchPatient(term)),
      );
      return searchResults.find((result) => result !== null) || null;
    }
    return this.request.searchPatient(searchTerm);
  }

  
}
